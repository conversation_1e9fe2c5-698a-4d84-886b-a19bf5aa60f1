#include <iostream>
#include <string>
using namespace std;

class Main {
public:
int climbStairs(int n) {
    // Write your code here
                       return 0;
                     }
                 };

                 int main() {
                    string input;
                     getline(cin, input);
                     int n = stoi(input);

                     Main mainObj; // Use Main class instead of Solution
                     int result = mainObj.climbStairs(n);

                     cout << result << endl;
                     return 0;
                 };