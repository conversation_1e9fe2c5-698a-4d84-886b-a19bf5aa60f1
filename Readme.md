# AlgoArena Server

Backend server implementation for the AlgoArena coding platform.

## Overview

This server provides the API endpoints and core functionality for running and evaluating code submissions, managing user accounts, and handling programming challenges.

## Getting Started

### Prerequisites
- Node.js (v14 or higher)
- MongoDB
- Docker (optional)

### Installation
1. Clone the repository
```bash
git clone https://github.com/yourusername/algoArena_server.git
cd algoArena_server
```

2. Install dependencies
```bash
npm install
```

3. Start the server
```bash
npm start
```

## Features
- Code execution engine
- User authentication and authorization
- Problem set management
- Submission history tracking
- Real-time evaluation system

## API Documentation
API endpoints are documented using Swagger/OpenAPI. Access the documentation at:
```
http://localhost:3000/api-docs
```

## License
This project is licensed under the MIT License.

## Contact
For questions and support, please open an issue in the repository.