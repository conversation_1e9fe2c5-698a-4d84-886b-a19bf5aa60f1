import axios from "axios";
import { myEnvironment } from "../config/env.js";

// get judge0 language id
export const getJudge0LanguageId = (language) => {
  const languageMap = {
    PYTHON: 71,
    JAVA: 62,
    JAVASCRIPT: 63,
    CPP: 54,
  };
  return languageMap[language.toUpperCase()];
};

// API headers
const headers = {
  "Content-Type": "application/json",
};

// submit batch
export const submitBatch = async (submissions) => {
  const url = `${myEnvironment.JUDGE0_API_URL}/submissions/batch?base64_encoded=false`;
  console.log(url);
  try {
    const { data } = await axios.post(url, { submissions }, { headers });
    console.log("Submission result:", data);
    return data;
  } catch (error) {
    console.error("Error submitting batch:", error.response?.data || error);
    throw error;
  }
};

// sleep
const sleep = (ms) => new Promise((resolve) => setTimeout(resolve, ms));

// poll batch
export const pollBatchResults = async (tokens) => {
  while (true) {
    const { data } = await axios.get(
      `${myEnvironment.JUDGE0_API_URL}/submissions/batch`,
      {
        params: { tokens: tokens.join(","), base64_encoded: false },
      }
    );

    console.log(data);
    const results = data.submissions;
    const isAllDone = results.every(
      (result) => result.status.id !== 1 && result.status.id !== 2
    );
    if (isAllDone) {
      return results;
    }
    await sleep(1000);
  }
};

export function getLanguageName(languageId) {
  const LANGUAGE_NAMES = {
    74: "TypeScript",
    63: "JavaScript",
    71: "Python",
    62: "Java",
    54: "CPP",
  };

  return LANGUAGE_NAMES[languageId] || "Unknown";
}
