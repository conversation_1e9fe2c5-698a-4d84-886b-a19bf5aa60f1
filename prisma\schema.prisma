generator client {
  provider = "prisma-client-js"
  output   = "../src/generated/prisma"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

enum UserRole {
  USER
  ADMIN
}

enum Difficulty {
  EASY
  MEDIUM
  HARD
}

enum Visibility {
  Public
  Private
}

model Potd {
  id        String   @id @default(uuid())
  date      DateTime @unique
  problemId String
  userId    String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  problem     Problem  @relation(fields: [problemId], references: [id], onDelete: Cascade)
  solvedUsers String[]
}

model YearlyGrid {
  id     String   @id @default(uuid())
  user   User     @relation(fields: [userId], references: [id])
  userId String
  date   DateTime
}

model User {
  id             String          @id @default(uuid())
  name           String?
  username       String          @unique
  email          String          @unique
  image          String?
  role           UserRole        @default(USER)
  password       String?
  localPassword  Boolean         @default(false)
  token          String?
  otp            String?
  bio            String?
  currentStreak  Int             @default(0)
  maxStreak      Int             @default(0)
  lastSubmission DateTime? // to check streak continuity
  isVerified     Boolean         @default(false)
  createdAt      DateTime        @default(now())
  updatedAt      DateTime        @updatedAt
  problems       Problem[]
  submission     Submission[]
  problemSolved  ProblemSolved[]
  sheets         Sheet[]
  links          Json?
  yearlyGrid     YearlyGrid[]
  achievements   Json?           @default("{}")
  badges         Json?           @default("{}")
  xp             Int?            @default(0)
  tier           String          @default("Bronze")
  level          Int?            @default(0)
  hintsUsed      String[]
  editorialUsed  String[]
}

model Problem {
  id                 String           @id @default(uuid())
  title              String
  description        String
  difficulty         Difficulty
  tags               String[]
  userId             String
  examples           Json
  constraints        String
  hints              Json?
  editorial          Json?
  privateTestcases   Json             @default("[]")
  publicTestcases    Json             @default("[]")
  codeSnippets       Json
  referenceSolutions Json
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  user               User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  submission         Submission[]
  solvedBy           ProblemSolved[]
  problemsSheets     ProblemInSheet[]
  potd               Potd[]
  companyTags        Json?
}

model Submission {
  id            String  @id @default(uuid())
  userId        String
  problemId     String
  sourceCode    Json
  language      String
  stdin         String?
  stdout        String?
  stderr        String?
  compileOutput String?
  status        String
  memory        String?
  time          String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  problem Problem @relation(fields: [problemId], references: [id], onDelete: Cascade)

  testCases TestCaseResult[]
}

model TestCaseResult {
  id            String  @id @default(uuid())
  submissionId  String
  testCase      Int
  passed        Boolean
  stdout        String?
  expected      String
  stderr        String?
  compileOutput String?
  status        String
  memory        String?
  time          String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  submission Submission @relation(fields: [submissionId], references: [id], onDelete: Cascade)

  @@index([submissionId])
}

model ProblemSolved {
  id        String @id @default(uuid())
  userId    String
  problemId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  problem Problem @relation(fields: [problemId], references: [id], onDelete: Cascade)

  @@unique([userId, problemId])
}

model Sheet {
  id          String     @id @default(uuid())
  name        String
  description String?
  userId      String
  visibility  Visibility @default(Public)
  likes       String[]
  tags        String[]

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  problems ProblemInSheet[]

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([name, userId])
}

model ProblemInSheet {
  id        String @id @default(uuid())
  sheetId   String
  problemId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  sheet   Sheet   @relation(fields: [sheetId], references: [id], onDelete: Cascade)
  problem Problem @relation(fields: [problemId], references: [id], onDelete: Cascade)

  @@unique([sheetId, problemId])
}
